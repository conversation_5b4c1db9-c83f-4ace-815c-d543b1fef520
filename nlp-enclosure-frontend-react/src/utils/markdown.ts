import markdownit from 'markdown-it';

// 创建markdown实例
export const md = markdownit({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
});

// 配置markdown渲染规则
md.renderer.rules.code_inline = function (tokens, idx) {
  const token = tokens[idx];
  return `<code class="inline-code">${md.utils.escapeHtml(token.content)}</code>`;
};

md.renderer.rules.code_block = function (tokens, idx) {
  const token = tokens[idx];
  return `<pre class="code-block"><code>${md.utils.escapeHtml(token.content)}</code></pre>`;
};

md.renderer.rules.fence = function (tokens, idx) {
  const token = tokens[idx];
  const info = token.info ? md.utils.unescapeAll(token.info).trim() : '';
  const langName = info ? info.split(/\s+/g)[0] : '';
  
  return `<pre class="code-block"><code class="language-${langName}">${md.utils.escapeHtml(token.content)}</code></pre>`;
};
