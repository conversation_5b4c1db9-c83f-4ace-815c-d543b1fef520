import { Suspense, lazy } from 'react';
import App from '../App.tsx';
import ScrollList from '../pages/overdue/scrollList/index.tsx';
import Independent from "../pages/home";
import UserLogin from "../pages/userLogin";
import IdaasLogin from "../pages/idaasLogin";
import QywxLogin from "../pages/qywxLogin";
import SuperSonic from "../pages/supersonic";
import { Navigate } from 'react-router-dom';
import ChromeApp from "../pages/chromeApp";
import WpsApp from "../pages/wpsApp";
// import Theme from "../pages/theme/index";
// import FilterList from "../pages/filterList/index.tsx";

// const Home = () => import("../pages/home.tsx");
// react router6 + react lazy 延迟加载的正确写法
const FilterList = lazy(() => import('../pages/overdue/filterList'));
const Theme = lazy(() => import('../pages/overdue/theme'));

export const DefaultRoutes = [
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <Navigate to="home" replace />
      },
      {
        path: 'home',
        name: '主页',
        element: <Independent/>
      },
      {
        path: 'chromeApp',
        name: '插件首页',
        element: <ChromeApp/>
      },
      {
        path: 'wpsApp',
        name: 'WPS首页',
        element: <WpsApp/>
      },
      {
        path: 'userLogin',
        name: '登录',
        element: <UserLogin/>
      },
      {
        path: 'idaasLogin',
        name: 'IDaaS登录',
        element: <IdaasLogin/>
      },
      {
        path: 'qywxLogin',
        name: '企业微信登录',
        element: <QywxLogin/>
      },
      {
        path: 'filterList',
        name: '条件筛选+无限滚动列表',
        element: (
          <Suspense fallback={<h1>loading</h1>}>
            <FilterList />
          </Suspense>
        )
        // lazy: () => <FilterList />,
      },
      {
        path: 'theme',
        name: '多种主题切换',
        element: (
          <Suspense fallback={<h1>loading</h1>}>
            {/* 这里的Suspense就是只对Theme起作用 */}
            <Theme />
          </Suspense>
        )
      },
      {
        path: 'slist',
        name: '向上滚动加载的list',
        element: (
          <Suspense fallback={<h1>loading</h1>}>
            <ScrollList />
          </Suspense>
        )
      },
      {
        path: 'supersonic',
        name: 'SuperSonic 问答对话',
        element: <SuperSonic />
      }
    ]
  }
];
