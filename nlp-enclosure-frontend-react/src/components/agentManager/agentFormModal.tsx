import {<PERSON><PERSON>, <PERSON><PERSON>, Col, Config<PERSON>rovider, Form, Input, message, Modal, Row, Spin, Select, Switch, Tooltip} from 'antd';
import {AgentDict, ColorPair, colorPairs} from "../../types/agentDict.ts";
import {getInfo} from "../../services/difyApi.ts";
import React, { useEffect } from "react";
import {save} from "../../services/agentDict.ts";
import { createDefaultSuperSonicAgent } from "../../services/supersonic.ts";
import { InfoCircleOutlined } from "@ant-design/icons";
import IconSelect from "../IconSelect";

const { TextArea } = Input;
const { Option } = Select;

// Agent类型选项
const agentTypes = [
    { value: 'default', label: '默认' },
    { value: 'supersonic', label: 'SuperSonic' }, // 新增的SuperSonic类型
];

interface AgentFormModalProps {
    isOpen: boolean;
    onClose: () => void;
    agentFormData?: AgentDict | null;
    onSubmit: () => void;
    isCanCreateAgent: boolean;
    // onEdit 是可选的，因为在某些情况下可能不需要
    onEdit?: (agent: AgentDict) => void;
}

const AgentFormModal: React.FC<AgentFormModalProps> = ({ isOpen, onClose, agentFormData, onSubmit, onEdit }) => {
    const [form] = Form.useForm<AgentDict>();

    const [isShowSpin, setIsShowSpin] = React.useState(false);
    // 是否使用 SuperSonic 内置 DEMO
    const [useBuiltInDemo, setUseBuiltInDemo] = React.useState(false);

    const nameValue = Form.useWatch('name', form);
    const typeValue = Form.useWatch('type', form);
    const iconValue = Form.useWatch('icon', form);
    const descriptionValue = Form.useWatch('description', form);
    const apiKeyValue = Form.useWatch('apiKey', form);
    const apiServerHostValue = Form.useWatch('apiServerHost', form);

    // 在组件挂载或更新时检查初始值
    useEffect(() => {
        // 每次弹窗打开或关闭时处理表单
        if (!isOpen) {
            // 弹窗关闭时，重置表单和状态
            form.resetFields();
            setUseBuiltInDemo(false);
            return;
        }
        // 强制重置表单，确保没有残留的数据
        form.resetFields();

        // 延迟一下再设置表单值，确保重置生效
        setTimeout(() => {
            // 如果是新增（agentFormData 为 null），设置默认值
            if (!agentFormData) {
                // 设置默认类型和随机图标
                form.setFieldsValue({
                    type: 'default',
                    icon: getRandomIcon()
                });
                // 确保其他状态也被重置
                setUseBuiltInDemo(false);
                return;
            }
        }, 0);


        // 延迟一下再处理编辑逻辑，确保重置生效
        setTimeout(() => {
            // 如果是编辑现有 Agent
            if (agentFormData) {
                if (agentFormData.type === 'supersonic') {
                    // 如果是 SuperSonic 类型，检查是否使用内置 DEMO
                    const isBuiltInDemo = agentFormData.apiKey === 'supersonic-demo-key' ||
                                        agentFormData.apiServerHost === 'https://api.supersonic.ai';
                    setUseBuiltInDemo(isBuiltInDemo);

                    // 确保表单中有所有必要的字段
                    form.setFieldsValue({
                        ...agentFormData,
                        // 如果缺少 SuperSonic 特有字段，使用默认值
                        superSonicServerHost: agentFormData.superSonicServerHost || 'localhost:9080',
                        superSonicAgentId: agentFormData.superSonicAgentId || 'supersonic-assistant-general'
                    });
                } else {
                    // 非 SuperSonic 类型，直接设置表单值
                    form.setFieldsValue(agentFormData);
                }
            }
        }, 0);
    }, [agentFormData, form, isOpen]);

    // 监听类型变化，当类型为 'supersonic' 时自动启用内置 DEMO
    useEffect(() => {
        if (typeValue === 'supersonic' && !agentFormData) {
            // 如果是新建 SuperSonic 类型的 Agent，默认启用内置 DEMO
            setUseBuiltInDemo(true);

            // 自动填充默认的 SuperSonic Agent 配置
            const defaultAgent = createDefaultSuperSonicAgent();
            form.setFieldsValue({
                name: defaultAgent.name,
                description: defaultAgent.description,
                apiKey: defaultAgent.apiKey,
                apiServerHost: defaultAgent.apiServerHost,
                // 添加 SuperSonic 特有字段
                superSonicServerHost: defaultAgent.superSonicServerHost,
                superSonicAgentId: defaultAgent.superSonicAgentId
            });
        } else if (typeValue !== 'supersonic') {
            // 如果类型不是 SuperSonic，关闭内置 DEMO
            setUseBuiltInDemo(false);
        }
    }, [typeValue, form, agentFormData]);

    const handleOk = () => {
        form.validateFields().then(values => {
            setIsShowSpin(true);

            // 如果选择使用 SuperSonic 内置 DEMO
            if (values.type === 'supersonic' && useBuiltInDemo) {
                // 使用默认的 SuperSonic Agent 配置
                const defaultSuperSonicAgent = createDefaultSuperSonicAgent();

                // 保留用户输入的名称和描述（如果有）
                if (values.name) {
                    defaultSuperSonicAgent.name = values.name;
                }
                if (values.description) {
                    defaultSuperSonicAgent.description = values.description;
                }

                // 如果是编辑现有 Agent，保留 ID 和其他必要字段
                if (agentFormData && agentFormData.id) {
                    defaultSuperSonicAgent.id = agentFormData.id;
                    defaultSuperSonicAgent.isDefault = agentFormData.isDefault;
                    defaultSuperSonicAgent.sortOrder = agentFormData.sortOrder;
                    defaultSuperSonicAgent.icon = agentFormData.icon;
                }

                // 使用默认配置替换用户输入的值
                values = defaultSuperSonicAgent;
            } else {
                // 正常处理流程
                // 填充初始化的内容
                if (agentFormData) {
                    values.id = agentFormData.id;
                    values.isDefault = agentFormData.isDefault;
                    values.sortOrder = agentFormData.sortOrder;
                    if (!values.icon) {
                      values.icon = agentFormData.icon;
                      values.iconColor = agentFormData.iconColor;
                    }
                    values.type = agentFormData.type || 'default'; // 保留原有类型或设置为默认类型
                }

                if (!values.id) {
                    // 如果是SuperSonic类型，使用特定的颜色
                    if (values.type === 'supersonic') {
                        // 为SuperSonic类型设置特定的颜色
                        values.iconColor = "#00bfff"; // 深天蓝色
                        values.fontColor = "#ffffff"; // 白色文字
                    } else {
                        // 其他类型使用随机颜色
                        const colorPair = getRandomColorPair();
                        values.iconColor = colorPair.bgColor;
                        values.fontColor = colorPair.textColor; // 设置对应的文字颜色
                    }
                }

                // 如果没有设置图标，使用随机图标
                if (!values.icon) {
                    values.icon = getRandomIcon(); // 使用随机图标
                }
            }

            save(values).then(res => {
                message.success('保存成功');
                if (res) {
                    // 清空表单和状态
                    form.resetFields();
                    setUseBuiltInDemo(false);

                    // 如果是编辑操作且提供了 onEdit 回调，则调用 onEdit
                    if (agentFormData && agentFormData.id && onEdit) {
                        onEdit(values);
                    } else {
                        // 否则调用 onSubmit 回调
                        onSubmit();
                    }

                    // 关闭弹窗
                    onClose();
                }
            }).finally(() => {
                setIsShowSpin(false);
            })
        });
    };

    const handleCancel = () => {
        // 清空表单和状态
        form.resetFields();
        setUseBuiltInDemo(false);
        // 通知父组件关闭弹窗
        onClose();
    };

    const handleLoadApiParams = () => {
        setIsShowSpin(true);
        getInfo(apiServerHostValue, apiKeyValue).then(res => {
            if (res) {
                form.setFieldsValue({
                    name: res.name,
                    description: res.description,
                });
            }
        }).finally(() => {
            setIsShowSpin(false);
        })
    };

    const getRandomColorPair = (): ColorPair => {
        const randomIndex = Math.floor(Math.random() * colorPairs.length);
        return colorPairs[randomIndex];
    }

    // 随机选择一个默认图标
    const getRandomIcon = (): string => {
        const defaultIcons = ['🤖', '🧠', '💡', '🔍', '📚', '🌐', '💬', '🧩', '🎯', '🚀'];
        const randomIndex = Math.floor(Math.random() * defaultIcons.length);
        return defaultIcons[randomIndex];
    }

    return (
        <>
        <Modal
            title={agentFormData ? '编辑' : '新增'}
            open={isOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            okText={agentFormData ? '保存' : '创建'}
            cancelText="取消"
            destroyOnClose={true} // 关闭时销毁子元素
            afterClose={() => {
                // 弹窗关闭后清空表单和状态
                form.resetFields();
                setUseBuiltInDemo(false);
            }}
        >
            <div>
                <div style={{ display: 'flex', alignItems: 'center', height: '100%', margin: '20px 0' }}>
                    <Avatar size={50} style={{
                        backgroundColor: 'rgb(243 244 246)',
                        fontSize: 28,
                        color: 'rgb(169, 172, 170)',
                        marginRight: 12,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                    src={iconValue && (iconValue.startsWith('http://') || iconValue.startsWith('https://'))
                        ? iconValue
                        : undefined}
                    >
                        {iconValue && !(iconValue.startsWith('http://') || iconValue.startsWith('https://')) ? (
                            <span style={{ fontSize: '24px' }}>{iconValue}</span>
                        ) : (
                            !iconValue && (nameValue ? nameValue[0] : '*')
                        )}
                    </Avatar>
                    <span style={{fontSize: 22}}>{nameValue || '新 Agent'}</span>
                </div>
                <Form
                    layout="vertical"
                    form={form}
                    initialValues={agentFormData || { type: 'default' }}
                    size="large"
                    preserve={false} // 不保留表单字段值
                >
                     <Form.Item
                        label="Agent类型"
                        name="type"
                        rules={[{ required: true, message: '请选择Agent类型' }]}
                        initialValue="default" // 默认选择'default'类型
                    >
                        <Select placeholder="请选择Agent类型">
                            {agentTypes.map(type => (
                                <Option key={type.value} value={type.value}>{type.label}</Option>
                            ))}
                        </Select>
                    </Form.Item>

                    {/* 当选择 SuperSonic 类型时显示额外配置项 */}
                    {typeValue === 'supersonic' && (
                        <>
                            <Form.Item
                                label={
                                    <span>
                                        使用内置 DEMO
                                        <Tooltip title="启用后将使用 SuperSonic 内置的语义模型、问答助手和大模型连接，无需配置 API Key">
                                            <InfoCircleOutlined style={{ marginLeft: 8 }} />
                                        </Tooltip>
                                    </span>
                                }
                            >
                                <Switch
                                    checked={useBuiltInDemo}
                                    onChange={(checked) => {
                                        setUseBuiltInDemo(checked);
                                        if (checked) {
                                            // 如果启用内置 DEMO，自动填充默认配置
                                            const defaultAgent = createDefaultSuperSonicAgent();
                                            form.setFieldsValue({
                                                apiKey: defaultAgent.apiKey,
                                                apiServerHost: defaultAgent.apiServerHost,
                                                name: nameValue || defaultAgent.name,
                                                description: descriptionValue || defaultAgent.description,
                                                // 添加 SuperSonic 特有字段
                                                superSonicServerHost: defaultAgent.superSonicServerHost,
                                                superSonicAgentId: defaultAgent.superSonicAgentId,
                                                // 保留用户选择的图标或使用默认图标
                                                icon: iconValue || defaultAgent.icon
                                            });
                                        }
                                    }}
                                />
                            </Form.Item>
                            {/* agentId 配置项 */}
                            <Form.Item
                                label="Agent ID"
                                name="superSonicAgentId"
                                tooltip="SuperSonic 的 Agent ID"
                                rules={[{ required: !useBuiltInDemo, message: '请输入 SuperSonic 的 Agent ID' }]}
                            >
                                <Input
                                    placeholder="请输入 SuperSonic 的 Agent ID"
                                />
                            </Form.Item>
                        </>
                    )}
                    <Form.Item
                        label="API Key"
                        name="apiKey"
                        rules={[{ required: true, message: '请输入API Key' }]}
                    >
                        <Input placeholder="请输入API Key" />
                    </Form.Item>
                    <Form.Item
                        label="API Server Host"
                        required
                    >
                        <Row gutter={8}>
                            <Col span={18}>
                                <Form.Item
                                    name="apiServerHost"
                                    noStyle={true}
                                    rules={[{ required: true, message: '请输入API Server Host' }]}
                                >
                                    <Input placeholder="请输入API Server Host" />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Button onClick={handleLoadApiParams}>获取API信息</Button>
                            </Col>
                        </Row>
                    </Form.Item>
                    <Row gutter={16}>
                        <Col span={16}>
                            <Form.Item
                                label="名称"
                                name="name"
                                rules={[{ required: true, message: '请输入名称' }]}
                            >
                                <Input placeholder="请输入名称" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="图标"
                                name="icon"
                                tooltip="选择一个表情图标作为Agent的标识"
                            >
                                <IconSelect value={iconValue} onChange={(value) => {
                                  console.log("IconSelect", value);
                                  form.setFieldValue('icon', value)
                                }} />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item
                        label="描述"
                        name="description"
                        rules={[{ required: true, message: '请输入描述' }]}
                    >
                        <TextArea placeholder="请输入描述" rows={4} />
                    </Form.Item>
                </Form>
            </div>
        </Modal>
            <ConfigProvider
                theme={{
                    token: {
                        zIndexPopupBase: 3000,
                    }
                }}
            >
                <Spin spinning={isShowSpin} tip={'正在处理...'} fullscreen />
            </ConfigProvider>
        </>
    );
};

export default AgentFormModal;
